import React, { useState } from "react";
import "../App.css";
import useFetchPubMedData from "../hooks/useFetchPubMedData";
import {
  Diff<PERSON>ighlighter,
  NotFoundReferences,
  <PERSON>rror<PERSON>ot<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ReferenceQualityC<PERSON>cker,
  ReferenceStatistics,
  StatusBadge,
  ActionBadge,
  ProgressBadge
} from "../components/common";

import MultiSelectCheckbox from "./MultiSelect";
import downloadExcel from "./downloadExcel";

// import { terms } from "../constants/term";
import { Document, Packer, Paragraph } from "docx";
import { saveAs } from "file-saver";

const downloadDocx = async (references) => {
  const doc = new Document({
    sections: [
      {
        properties: {},
        children: references.map((ref) => {
          console.log(ref, "ref");

          return new Paragraph({
            text: `${ref.ind + 1}. ${ref.finalStr || ref.term}`,
          });
        }),
      },
    ],
  });

  const blob = await Packer.toBlob(doc);
  saveAs(blob, "references.docx");
};

const PubMedComponent = ({ terms }) => {
  const [prog, setProg] = useState(0);
  const hasTestParam = new URLSearchParams(window.location.search).has("test");
  const [isDiffViewerOpen, setIsDiffViewerOpen] = React.useState(false);
  const [showStatistics, setShowStatistics] = useState(false);
  const [activeFilter, setActiveFilter] = useState('all');

  const { data, loading, error, setData } = useFetchPubMedData(
    terms.data,
    true,
    setProg
  );
  const [rowSelections, setRowSelections] = useState({});
  const handleCheckboxChange = (option, elem) => {
    const rowId = elem.ind;
    setRowSelections((prev) => {
      const currentRowSelections = prev[rowId] || [];
      const newRowSelections = currentRowSelections.includes(option)
        ? currentRowSelections.filter((item) => item !== option)
        : [...currentRowSelections, option];
      if (data.found[rowId]) {
        data.found[rowId].incorrect = newRowSelections;
      }
      return {
        ...prev,
        [rowId]: newRowSelections,
      };
    });
  };

  // Filter function
  const handleFilterChange = (filterType) => {
    setActiveFilter(filterType);
  };

  // Filter the data based on active filter
  const getFilteredData = () => {
    if (!data?.found) return [];

    switch (activeFilter) {
      case 'pubmed':
        return data.found.filter(ref => ref.type === 'FOUND');
      case 'crossref':
        return data.found.filter(ref => ref.type === 'CROSSREF');
      case 'not-found':
        return data.found.filter(ref => ref.type === 'NOT_FOUND');
      case 'duplicates':
        return data.found.filter(ref => ref.MarkType === 'DUPLICATE');
      case 'urls':
        return data.found.filter(ref => ref.type === 'URL');
      case 'all':
      default:
        return data.found;
    }
  };

  const filteredData = getFilteredData();

  const handleReferenceEdit = (index, newText) => {
    setData(prevData => ({
      ...prevData,
      found: prevData.found.map(ref =>
        ref.ind === index ? { ...ref, finalStr: newText } : ref
      )
    }));
  };



  if (terms.isLoading === 2 && terms.data.length === 0)
    return <NotFoundReferences />;
  if (error) return <ErrorNotification message={error} />;
  if (typeof data === "string") return <ErrorNotification message={data} />;
  return (
    <>
      {loading && (
        <div className="loading-container">
          <LoaderSpinner />
          <ProgressBadge progress={prog} />
          <p>Processing {terms.data.length} references...</p>
        </div>
      )}

      {!loading && (
        <>
          <div className="found-citation-wrapper">
            <div className="results-header">
              <h3 className="text-lg font-semibold text-gray-900">Enhanced Citations</h3>
            </div>
            <div className="action-icons">
              <button
                className="icon-button"
                onClick={() => {
                  setIsDiffViewerOpen((pre) => !pre);
                }}
                title={isDiffViewerOpen ? "Hide Diff" : "Show Diff"}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                </svg>
              </button>
              <button
                className="icon-button"
                onClick={() => downloadDocx(data.found)}
                title="Download DOCX"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </button>
              {hasTestParam && (
                <button
                  className="icon-button"
                  onClick={() => downloadExcel(data.found, "references.xlsx")}
                  title="Download Report"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </button>
              )}
              <button
                className="icon-button"
                onClick={() => setShowStatistics(!showStatistics)}
                title={showStatistics ? 'Hide Statistics' : 'Show Statistics'}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </button>
            </div>
          </div>
        </>
      )}

      {!loading && showStatistics && (
        <ReferenceStatistics
          references={data.found}
          onFilterChange={handleFilterChange}
          activeFilter={activeFilter}
        />
      )}

      {!loading && (
        <table className="table">
          <thead>
            <tr>
              <th>Reference</th>
              {isDiffViewerOpen ? (
                <th>Comparison</th>
              ) : (
                <>
                  <th>Original Citation</th>
                  <th>Enhanced Citation</th>
                  {hasTestParam && (
                    <th>Actions</th>
                  )}
                </>
              )}
            </tr>
          </thead>
          <tbody>
            {data?.found?.map((elem, ind) => (
              <DetailedBox
                elem={elem}
                key={"found" + ind}
                isDiffViewerOpen={isDiffViewerOpen}
                selectedOptions={rowSelections[elem.ind] || []}
                handleCheckboxChange={handleCheckboxChange}
                onReferenceEdit={handleReferenceEdit}
              />
            ))}
          </tbody>
        </table>
      )}
    </>
  );
};

// Add this helper function before DetailedBox component
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text);
};

const searchInPubMed = (text) => {
  const searchTerm = encodeURIComponent(text);
  window.open(`https://pubmed.ncbi.nlm.nih.gov/?term=${searchTerm}`, "_blank");
};

// Modify the DetailedBox component's td that shows finalStr
const DetailedBox = ({
  elem,
  isDiffViewerOpen,
  handleCheckboxChange,
  selectedOptions,
  onReferenceEdit,
}) => {
  const hasTestParam = new URLSearchParams(window.location.search).has("test");
  if (typeof elem === "string") {
    return;
  }

  // Debug: Log element data to check for duplicate properties
  if (elem.MarkType === 'DUPLICATE' || elem.type === 'DUPLICATE') {
    console.log('Duplicate element:', elem);
  }

  return (
    <tr>
      <td>
        <div className="badge-container">
          <span className="reference-number">{elem.ind + 1}</span>
          <StatusBadge
            type={
              elem.MarkType === 'DUPLICATE' || elem.type === 'DUPLICATE'
                ? 'DUPLICATE'
                : (elem.type || elem.MarkType || 'FOUND')
            }
            duplicateOf={elem.duplicateOf}
          />
        </div>
      </td>
      {isDiffViewerOpen ? (
        <td>
          <DiffHighlighter
            text1={elem.term}
            text2={elem.finalStr ?? elem.term}
          />{" "}
        </td>
      ) : (
        <>
          <td>
            <div className="reference-content">
              <div className="reference-text">{elem.term}</div>
              <div className="badge-container">
                <ActionBadge
                  action="search"
                  onClick={() => searchInPubMed(elem.term)}
                />
              </div>
            </div>
          </td>
          <td>
            <div className="reference-content">
              <div className="reference-main">
                <ReferenceEditor
                  reference={elem}
                  onSave={onReferenceEdit}
                />
              </div>
              <div className="badge-container">
                <ReferenceQualityChecker reference={elem} />
                <ActionBadge
                  action="copy"
                  onClick={() => copyToClipboard(elem.finalStr)}
                />
              </div>
            </div>
          </td>
          {hasTestParam && (
            <td>
              <MultiSelectCheckbox
                {...{ elem, handleCheckboxChange, selectedOptions }}
              />
            </td>
          )}
        </>
      )}
    </tr>
  );
};

export default PubMedComponent;
