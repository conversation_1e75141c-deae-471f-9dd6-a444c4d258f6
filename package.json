{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@ckeditor/ckeditor5-react": "^9.0.0", "@tailwindcss/postcss": "^4.1.10", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "aieditor": "^1.0.15", "axios": "^1.7.9", "ckeditor5": "^43.0.0", "ckeditor5-premium-features": "^43.0.0", "diff": "^7.0.0", "docx": "^9.4.1", "file-saver": "^2.0.5", "postcss": "^8.5.6", "react": "^18.2.0", "react-diff-viewer": "^3.1.1", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "tailwindcss": "^4.1.10", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "proxy": "http://localhost:5000", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}