import React from 'react';
import '../../App.css';

/**
 * Modern Status Badge Component
 * Replaces CSS pseudo-elements with proper React components
 */
const StatusBadge = ({ type, duplicateOf, className = "" }) => {
  const getBadgeConfig = () => {
    switch (type) {
      case 'FOUND':
        return {
          text: 'PUBMED',
          className: 'badge-pubmed',
          icon: '🔬'
        };
      case 'CROSSREF':
        return {
          text: 'CROSSREF',
          className: 'badge-crossref',
          icon: '🔗'
        };
      case 'NOT_FOUND':
        return {
          text: 'NOT FOUND',
          className: 'badge-not-found',
          icon: '❓'
        };
      case 'URL':
        return {
          text: 'URL',
          className: 'badge-url',
          icon: '🌐'
        };
      case 'DUPLICATE':
        return {
          text: duplicateOf && duplicateOf.length > 0 
            ? `DUPLICATE-${duplicateOf.join(',')}`
            : 'DUPLICATE',
          className: 'badge-duplicate',
          icon: '🔄'
        };
      case 'MULTIPLE_PubMed':
        return {
          text: 'MULTIPLE',
          className: 'badge-multiple',
          icon: '📚'
        };
      default:
        return null;
    }
  };

  const config = getBadgeConfig();

  // Debug: Log when we can't find a config
  if (!config) {
    console.log('No badge config found for type:', type);
    return null;
  }

  return (
    <span className={`status-badge ${config.className} ${className}`}>
      <span className="badge-icon">{config.icon}</span>
      <span className="badge-text">{config.text}</span>
    </span>
  );
};

/**
 * Quality Badge Component
 * Shows quality score with appropriate styling
 */
export const QualityBadge = ({ score, breakdown, className = "" }) => {
  const getQualityConfig = (score) => {
    if (score >= 85) return { className: 'badge-excellent', icon: '🌟', label: 'Excellent' };
    if (score >= 70) return { className: 'badge-good', icon: '✅', label: 'Good' };
    if (score >= 50) return { className: 'badge-warning', icon: '⚠️', label: 'Fair' };
    return { className: 'badge-poor', icon: '❌', label: 'Poor' };
  };

  const config = getQualityConfig(score);

  return (
    <span 
      className={`quality-badge ${config.className} ${className}`}
      title={breakdown ? 
        `Quality: ${score}%\nImprovement: ${breakdown.improvement}%\nStructure: ${breakdown.structure}%\nCompleteness: ${breakdown.completeness}%` 
        : `Quality: ${score}%`
      }
    >
      <span className="badge-icon">{config.icon}</span>
      <span className="badge-text">{score}%</span>
    </span>
  );
};

/**
 * Action Badge Component
 * For interactive actions like edit, copy, etc.
 */
export const ActionBadge = ({ action, onClick, disabled = false, className = "" }) => {
  const getActionConfig = () => {
    switch (action) {
      case 'edit':
        return { icon: '✏️', text: 'Edit', className: 'badge-edit' };
      case 'copy':
        return { icon: '📋', text: 'Copy', className: 'badge-copy' };
      case 'search':
        return { icon: '🔍', text: 'Search', className: 'badge-search' };
      case 'delete':
        return { icon: '🗑️', text: 'Delete', className: 'badge-delete' };
      case 'save':
        return { icon: '💾', text: 'Save', className: 'badge-save' };
      case 'cancel':
        return { icon: '❌', text: 'Cancel', className: 'badge-cancel' };
      default:
        return { icon: '⚡', text: action, className: 'badge-default' };
    }
  };

  const config = getActionConfig();

  return (
    <button
      className={`action-badge ${config.className} ${className} ${disabled ? 'disabled' : ''}`}
      onClick={onClick}
      disabled={disabled}
      title={config.text}
    >
      <span className="badge-icon">{config.icon}</span>
      <span className="badge-text">{config.text}</span>
    </button>
  );
};

/**
 * Progress Badge Component
 * Shows progress with visual indicator
 */
export const ProgressBadge = ({ progress, className = "" }) => {
  return (
    <div className={`progress-badge ${className}`}>
      <div className="progress-bar-container">
        <div 
          className="progress-bar-fill" 
          style={{ width: `${progress}%` }}
        />
      </div>
      <span className="progress-text">{progress}%</span>
    </div>
  );
};

export default StatusBadge;
