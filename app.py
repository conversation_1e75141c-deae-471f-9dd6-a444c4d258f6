import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from werkzeug.utils import secure_filename
import docx
import unicodedata
import os
import time
import re
import requests
import json
from dotenv import load_dotenv  # Load environment variables from .env

load_dotenv()

# Initialize Flask app and CORS
app = Flask(__name__)
CORS(app)

UPLOAD_FOLDER = 'uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Configure Logging
logging.basicConfig(
    filename='app.log',
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)


@app.route('/test', methods=['GET'])
def test_get():
    return jsonify({"message": "Test route working"}), 200


@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"error": "No file part in the request"}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "No file selected for uploading"}), 400

    if file and file.filename.endswith('.docx'):
        filename = secure_filename(file.filename)
        timestamp = int(time.time())  # Avoid overwriting
        filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        try:
            file.save(filepath)
            references = extract_references(filepath)

            if not references:
                return jsonify({"error": "No references found"}), 400

            return jsonify({"references": references})

        except Exception as e:
            logging.error(f"Error processing file {filename}: {e}")
            return jsonify({"error": "Internal server error"}), 500

    return jsonify({"error": "Only .docx files are allowed"}), 400

def extract_references(filepath):
    try:
        doc = docx.Document(filepath)
        references = []
        is_reference_section = False

        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()

            if not is_reference_section:
            # Detect the start of the References section (handling variations)
                if "references" in text.lower() or "bibliography" in text.lower() or "references:" in text.lower():
                    print("References Section:", text)  # Debug print
                    is_reference_section = True
                    continue

            # Stop processing if another major section starts
            if is_reference_section:
                if any(text.lower().startswith(kw) for kw in ["figure", "tables", "acknowledgement"]):
                    print("Stopping at section:", text)
                    break
            # Add valid reference lines, cleaned of Unicode
                if text:
                    print("Valid reference:", text)  # Debug print
                    cleaned_text = unicodedata.normalize("NFKD", text)
                    references.append(cleaned_text)

        return references if references else ["No references found"]
    except Exception as e:
        logging.error(f"Error extracting references: {e}")
        return []


API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"

def extract_details(content):
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}",
    }
    
    payload = {
        "model": "gpt-4o-mini",
        "messages": [
            {"role": "system", "content": "You are an assistant that extracts structured data from strings."},
            {"role": "user", "content": content},
        ],
        "max_tokens": 500,
    }
    
    try:
        response = requests.post(OPENAI_API_URL, headers=headers, json=payload)
        result = response.json()

        if response.status_code == 200 and "choices" in result and result["choices"]:
            content = result["choices"][0]["message"]["content"].strip()
            content = content.replace("```json", "").replace("```", "").strip()  # Clean extraneous markdown
            return json.loads(content)  # Convert string to JSON
        else:
            return {"error": "Failed to extract details", "response": result}
    
    except Exception as e:
        return {"error": "Error interacting with OpenAI API", "details": str(e)}

@app.route("/extract", methods=["POST"])
def extract():
    data = request.json
    
    extracted_data = extract_details(data["content"])  # Convert string function to callable
    return jsonify(extracted_data)

@app.route("/update-journal-map", methods=["POST"])
def update_journal_map():
    """
    Updates the journalMap.json file with new journal mappings
    """
    try:
        data = request.json
        original_journal = data.get('originalJournal')
        abbreviated_journal = data.get('abbreviatedJournal')
        
        # Validate input
        if not original_journal or not abbreviated_journal:
            return jsonify({
                'success': False,
                'error': 'Both originalJournal and abbreviatedJournal are required'
            }), 400
        
        print(f"📡 API Request: Add '{original_journal}' -> '{abbreviated_journal}'")
        
        # Path to the journalMap.json file
        journal_map_path = os.path.join('src', 'services', 'helpers', 'journalMap.json')
        
        # Read current journalMap.json
        current_map = {}
        try:
            if os.path.exists(journal_map_path):
                with open(journal_map_path, 'r', encoding='utf-8') as f:
                    current_map = json.load(f)
        except Exception as parse_error:
            print(f'Error reading journalMap.json: {parse_error}')
            return jsonify({
                'success': False,
                'error': 'Failed to read existing journal map'
            }), 500
        
        # Check if mapping already exists
        if original_journal in current_map:
            print(f"✅ Mapping already exists: '{original_journal}' -> '{current_map[original_journal]}'")
            return jsonify({
                'success': True,
                'message': 'Mapping already exists',
                'existing': current_map[original_journal],
                'totalMappings': len(current_map)
            })
        
        # Add new mapping
        current_map[original_journal] = abbreviated_journal
        
        # Sort the map alphabetically for better organization
        sorted_map = dict(sorted(current_map.items()))
        
        # Write back to file with proper formatting
        try:
            os.makedirs(os.path.dirname(journal_map_path), exist_ok=True)
            with open(journal_map_path, 'w', encoding='utf-8') as f:
                json.dump(sorted_map, f, indent=2, ensure_ascii=False)
            print(f"📝 Successfully added to journalMap.json: '{original_journal}' -> '{abbreviated_journal}'")
        except Exception as write_error:
            print(f'Error writing to journalMap.json: {write_error}')
            return jsonify({
                'success': False,
                'error': 'Failed to write to journal map file'
            }), 500
        
        # Success response
        return jsonify({
            'success': True,
            'message': 'Journal mapping added successfully',
            'added': {
                'originalJournal': original_journal,
                'abbreviatedJournal': abbreviated_journal
            },
            'totalMappings': len(sorted_map)
        })
        
    except Exception as error:
        print(f'Error in update-journal-map endpoint: {error}')
        return jsonify({
            'success': False,
            'error': str(error)
        }), 500

@app.route("/journal-map", methods=["GET"])
def get_journal_map():
    """
    Returns the current journal map
    """
    try:
        journal_map_path = os.path.join('src', 'services', 'helpers', 'journalMap.json')
        
        if not os.path.exists(journal_map_path):
            return jsonify({
                'success': True,
                'data': {},
                'totalMappings': 0
            })
        
        with open(journal_map_path, 'r', encoding='utf-8') as f:
            journal_map = json.load(f)
        
        return jsonify({
            'success': True,
            'data': journal_map,
            'totalMappings': len(journal_map)
        })
        
    except Exception as error:
        print(f'Error reading journal map: {error}')
        return jsonify({
            'success': False,
            'error': str(error)
        }), 500

if __name__ == "__main__":
    app.run(debug=True, port=4999)
