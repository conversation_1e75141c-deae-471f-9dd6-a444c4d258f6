
/* Modern table styles */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 20px 0;
  font-size: 14px;
  text-align: left;
  background-color: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

/* Modern table header styles */
.table th {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-weight: 600;
  padding: 16px 20px;
  border: none;
  text-align: left;
  font-size: 13px;
  letter-spacing: 0.5px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table th:first-child {
  border-top-left-radius: 12px;
}

.table th:last-child {
  border-top-right-radius: 12px;
}

/* Modern table row styles */
.table tr {
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f3f4;
}

.table tbody tr:hover {
  background-color: #f8f9ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table tbody tr:last-child {
  border-bottom: none;
}

/* Modern table cell styles */
.table td {
  padding: 20px;
  border: none;
  color: #2c3e50;
  word-wrap: break-word;
  position: relative;
  vertical-align: top;
  line-height: 1.5;
}

/* First column styling for reference numbers */
.table td:first-child {
  width: 120px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9ff 0%, #e9ecef 100%);
  font-weight: 600;
  border-right: 2px solid #e9ecef;
}

/* Button positioning within table cells */
.table td button {
  position: absolute;
  bottom: 8px;
  right: 8px;
}

/* Zebra stripe effect */
.table tr{
  background-color: #f2f2f2;
}

/* Responsive design for smaller screens */
@media screen and (max-width: 768px) {
  .table {
    font-size: 14px;
  }
  .table th,
  .table td {
    padding: 8px;
  }
}
/* .fixed-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.loader-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.8);
} */

/* .table tr[data-type="URL"] {background-color:#ffff4d} */
/* .table tr[data-type="NOT_FOUND"] {background-color:#4da9ff;position: relative;} */
/* .table tr[data-type="DUPLICATE"] {background-color:#ff4d4d} */

.table tr[data-type="NOT_FOUND"] td:nth-child(2),
.table tr[data-type="URL"] td:nth-child(2),
.table tr[data-type="CROSSREF"] td:nth-child(2),
.table tr[data-marktype="DUPLICATE"] td:nth-child(2) {
  position: relative;
}

.table tr[data-type="URL"] td:nth-child(2)::before {
  content: 'URL';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #0004fd;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}

.table tr[data-type="NOT_FOUND"] td:nth-child(2)::before {
  content: 'NOT_FOUND';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #4da9ff;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}

.table tr[data-type="CROSSREF"] td:nth-child(2)::before {
  content: 'CROSSREF';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #28a745;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}

.table tr[data-marktype="DUPLICATE"] td:nth-child(2)::before {
  content: 'DUPLICATE';
  position: absolute;
  top: 0px;
  right: 4px;
  background-color: #ff4d4d;
  color: white;
  font-size: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  pointer-events: none;
}


/* Modern loader spinner */
.loader {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(102, 126, 234, 0.1);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Reference Editor Styles */
.reference-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reference-text {
  flex: 1;
}

.reference-editor {
  width: 100%;
}

.reference-textarea {
  width: 100%;
  min-height: 60px;
  padding: 8px;
  border: 2px solid #007bff;
  border-radius: 4px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
}

.editor-buttons {
  display: flex;
  gap: 4px;
  margin-top: 4px;
}

.editor-help {
  margin-top: 4px;
  color: #666;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.action-button:hover {
  background-color: #f0f0f0;
}

.edit-button:hover {
  background-color: #e3f2fd;
}

.save-button:hover {
  background-color: #e8f5e8;
}

.cancel-button:hover {
  background-color: #ffebee;
}

/* Quality Indicator Styles */
.quality-indicator {
  display: inline-block;
  margin-left: 8px;
}

.quality-indicator.good span {
  color: #28a745;
}

.quality-indicator .warning {
  color: #ffc107;
}

.quality-indicator .error {
  color: #dc3545;
}

/* Reference Statistics Styles */
.reference-statistics {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: white;
  border-radius: 4px;
  border-left: 4px solid #dee2e6;
}

.stat-label {
  font-weight: 500;
}

.stat-value {
  font-weight: bold;
}

.stat-value.pubmed {
  color: #007bff;
}

.stat-value.crossref {
  color: #28a745;
}

.stat-value.not-found {
  color: #4da9ff;
}

.stat-value.duplicates {
  color: #ff4d4d;
}

.stat-value.urls {
  color: #0004fd;
}

/* Modern Badge Components */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.status-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.badge-icon {
  font-size: 12px;
}

.badge-text {
  font-size: 10px;
  font-weight: 600;
}

/* Status Badge Variants */
.badge-pubmed {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.badge-crossref {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

.badge-not-found {
  background: linear-gradient(135deg, #4da9ff, #2d8cff);
  color: white;
}

.badge-url {
  background: linear-gradient(135deg, #6f42c1, #5a2d91);
  color: white;
}

.badge-duplicate {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  animation: pulse 2s infinite;
}

.badge-multiple {
  background: linear-gradient(135deg, #fd7e14, #e55a00);
  color: white;
}

/* Quality Badge Variants */
.quality-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  margin: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: help;
}

.badge-excellent {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.badge-good {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.badge-warning {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
}

.badge-poor {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

/* Action Badge Variants */
.action-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border: none;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  margin: 2px;
  transition: all 0.2s ease;
  background: #f8f9fa;
  color: #495057;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-badge:hover:not(.disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.action-badge.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.badge-edit:hover {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.badge-copy:hover {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: white;
}

.badge-search:hover {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.badge-delete:hover {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.badge-save:hover {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

.badge-cancel:hover {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: white;
}

/* Progress Badge */
.progress-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
}

.progress-bar-container {
  width: 60px;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #28a745);
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progress-text {
  font-size: 10px;
  font-weight: 600;
  color: #495057;
}

/* Pulse animation for duplicates */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Badge container for multiple badges */
.badge-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

/* Old upload drawer styles removed - using new Tailwind-style drawer */



.results-section {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Quick Upload Toggle */
.quick-upload-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.quick-toggle-button {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
}

.quick-toggle-button:hover {
  background: linear-gradient(135deg, #20c997, #17a2b8);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

/* Reference content layout */
.reference-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Reference Editor Improvements */
.reference-display {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.reference-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.edit-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: background 0.2s ease;
}

.edit-button:hover {
  background: #0056b3;
}

.reference-main {
  flex: 1;
  min-width: 0; /* Allow text to wrap */
}

/* Enhanced table layout */
.table {
  table-layout: fixed;
  width: 100%;
}

.table td {
  vertical-align: top;
  padding: 20px;
}

.table td:nth-child(2) {
  width: 45%; /* Original reference */
}

.table td:nth-child(3) {
  width: 45%; /* Enhanced reference */
}

/* Responsive table adjustments */
@media screen and (max-width: 768px) {
  .table {
    font-size: 12px;
  }

  .table td {
    padding: 12px 8px;
  }

  .table td:first-child {
    width: 80px;
  }

  .toggle-button {
    padding: 6px 12px;
    font-size: 12px;
  }

  .download-wrapper {
    gap: 4px;
  }
}

/* Enhanced loading container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border-radius: 16px;
  margin: 20px 0;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.loading-container p {
  margin: 0;
  color: #667eea;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
}

.reference-text {
  flex: 1;
  line-height: 1.5;
  color: #2c3e50;
}

.reference-number {
  font-weight: 600;
  color: #667eea;
  margin-right: 8px;
  font-size: 14px;
}

/* Remove old CSS pseudo-element styles */
.table tr[data-type="NOT_FOUND"] td:nth-child(2)::before,
.table tr[data-type="URL"] td:nth-child(2)::before,
.table tr[data-type="CROSSREF"] td:nth-child(2)::before,
.table tr[data-marktype="DUPLICATE"] td:nth-child(2)::before {
  display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .status-badge,
  .quality-badge,
  .action-badge {
    font-size: 9px;
    padding: 3px 6px;
  }

  .badge-icon {
    font-size: 10px;
  }

  .reference-content {
    gap: 4px;
  }

  .badge-container {
    gap: 2px;
  }
}

.css-17vezug-marker{
  display: none !important;
}


/* td:first-child, th:first-child {
  display: none;
} */
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 120px);
}

.not-found-card {
  background-color: #eee;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
}

.not-found-title {
  color: #ff4d4d;
  font-size: 24px;
  margin-bottom: 10px;
}

.not-found-text {
  font-size: 16px;
  color: #555;
}

.upload{
  display: flex;
  justify-content: space-evenly;
  align-items: flex-start;
  gap: 20px;
  padding: 0;
  margin: 0;
}

/* Responsive upload layout */
@media (max-width: 768px) {
  .upload {
    flex-direction: column;
    gap: 20px;
  }
}


/* Legacy upload styles - updated for modern design */
.upload {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .upload {
    grid-template-columns: 1fr 1fr;
  }
}

.upload-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.upload-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.upload-input-container {
  margin: 0;
}

.upload-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.upload-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.upload-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.upload-description {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.upload-area {
  width: 100%;
  min-height: 8rem;
  padding: 0.75rem 1rem;
  border: 2px dashed #d1d5db;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  resize: vertical;
  transition: all 0.2s ease;
  font-family: inherit;
}

.upload-area:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.upload-area::placeholder {
  color: #9ca3af;
}

/* Compact button styles */
.toggle-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  letter-spacing: 0.3px;
}

.toggle-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.toggle-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

/* Compact download wrapper */
.download-wrapper {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  flex-wrap: wrap;
}

/* Results header wrapper - hidden during loading */
.found-citation-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.found-citation-wrapper.loading-hidden {
  display: none;
}

/* Smooth appearance animation for results */
.table {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Container for the entire results section */
.results-container {
  animation: fadeInUp 0.6s ease-out;
}

/* TE Reference Dashboard Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.5s ease-out;
}

/* Line clamp utility for text truncation */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar for better UX */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Enhanced hover effects for table rows */
.hover\:bg-slate-50:hover {
  background-color: #f8fafc;
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

/* TE Reference Dashboard Styles */
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.dashboard-header {
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #e2e8f0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.dashboard-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-button {
  padding: 0.5rem 1rem;
  background: #f1f5f9;
  color: #475569;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: #e2e8f0;
  transform: translateX(-2px);
}

.header-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-badge .icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #3b82f6;
}

.badge-text {
  font-size: 0.875rem;
  color: #64748b;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.upload-section {
  background: #ffffff;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

.upload-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-title .icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #3b82f6;
}

.upload-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .upload-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.upload-column {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.upload-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.upload-dropzone {
  position: relative;
  border: 2px dashed #d1d5db;
  border-radius: 0.75rem;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.dropzone-default:hover {
  border-color: #9ca3af;
}

.dropzone-active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.dropzone-success {
  border-color: #10b981;
  background-color: #f0fdf4;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.upload-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.upload-success .icon {
  width: 3rem;
  height: 3rem;
  color: #10b981;
}

.success-filename {
  font-size: 0.875rem;
  font-weight: 500;
  color: #047857;
  margin: 0;
}

.success-message {
  font-size: 0.75rem;
  color: #059669;
  margin: 0;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.upload-placeholder .icon {
  width: 3rem;
  height: 3rem;
  color: #9ca3af;
}

.placeholder-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.placeholder-subtitle {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

.text-input {
  width: 100%;
  height: 10rem;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  resize: none;
  transition: all 0.2s ease;
}

.text-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Submit Section */
.submit-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.submit-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.submit-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submit-button .icon {
  width: 1rem;
  height: 1rem;
}

.clear-button {
  padding: 0.75rem 1.5rem;
  background: #f1f5f9;
  color: #475569;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-button:hover {
  background: #e2e8f0;
}

.loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Results Section */
.results-section {
  background: #ffffff;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  transition: all 0.5s ease;
}

.results-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.results-title .icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #10b981;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
  gap: 1rem;
}

.large-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: #64748b;
  font-size: 0.875rem;
  margin: 0;
}

.table-container {
  overflow-x: auto;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.results-table {
  width: 100%;
  border-collapse: collapse;
  background: #ffffff;
}

.table-header {
  border-bottom: 1px solid #e5e7eb;
}

.table-th {
  text-align: left;
  padding: 0.75rem 1rem;
  font-weight: 500;
  color: #374151;
  background: #f9fafb;
  font-size: 0.875rem;
}

.index-column {
  width: 4rem;
}

.table-row {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background-color: #f8fafc;
}

.table-td {
  padding: 1rem;
  font-size: 0.875rem;
  color: #374151;
  vertical-align: top;
}

.index-cell {
  font-weight: 600;
  color: #1e293b;
  text-align: center;
}

.search-term-cell {
  max-width: 20rem;
}

.citation-cell {
  line-height: 1.5;
}

.truncate-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Empty States */
.empty-results {
  text-align: center;
  padding: 4rem 0;
}

.empty-results .icon {
  width: 4rem;
  height: 4rem;
  color: #9ca3af;
  margin: 0 auto 1rem;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.empty-subtitle {
  color: #64748b;
  margin: 0;
}

.empty-state {
  background: #ffffff;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 3rem;
  text-align: center;
}

.empty-state .icon {
  width: 4rem;
  height: 4rem;
  color: #9ca3af;
  margin: 0 auto 1rem;
}

.empty-state-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.empty-state-subtitle {
  color: #64748b;
  max-width: 28rem;
  margin: 0 auto;
  line-height: 1.5;
}

/* Clean Tailwind-style CSS */

/* Upload Drawer Styles */
.upload-drawer {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 24rem;
  z-index: 50;
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
}

.upload-drawer.open {
  transform: translateX(0);
}

.upload-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.upload-drawer.open .upload-drawer-overlay {
  opacity: 1;
}

.upload-drawer-content {
  position: relative;
  height: 100%;
  background-color: white;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 50;
  overflow-y: auto;
}

/* Fade-in animation */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .upload-drawer {
    width: 100%;
  }
}

/* Modern App.js Styles */
.demo-button {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.demo-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.upload-section {
  background: #ffffff;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  transition: all 0.3s ease;
  overflow: hidden;
}

.upload-section:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.upload-section.collapsed {
  margin-bottom: 1rem;
}

.upload-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9ff 0%, #e9ecef 100%);
  border-bottom: 1px solid #e9ecef;
}

.section-toggle-button {
  padding: 0.5rem 1rem;
  background: #f1f5f9;
  color: #475569;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.section-toggle-button:hover {
  background: #e2e8f0;
}

.upload-content {
  padding: 1.5rem;
}

.quick-toggle-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  margin-bottom: 1.5rem;
}

.quick-toggle-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
}

.quick-toggle-button .icon {
  width: 1rem;
  height: 1rem;
}

/* Modern Upload Components */
.file-upload-area {
  position: relative;
  border: 2px dashed #d1d5db;
  border-radius: 0.75rem;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fafafa;
}

.file-upload-area:hover {
  border-color: #9ca3af;
  background: #f5f5f5;
}

.file-upload-area .file-input {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

/* Responsive adjustments for main app */
@media screen and (max-width: 768px) {
  .header-flex {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .upload-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .demo-button {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
  }
}

.progress-container {
  width: 100%;
  background-color: #e5e7eb; /* Tailwind's bg-gray-200 */
  border-radius: 9999px;     /* Fully rounded */
  height: 20px;              /* Equivalent to h-2.5 (~30px) */
  margin-top: 1rem;          /* Equivalent to mt-4 */
  overflow: hidden;
}

.progress-bar {
  background-color: #43eb25; /* Tailwind's bg-blue-600 */
  height: 20px;
  border-radius: 9999px;
  transition: width 0.3s ease-in-out;
  text-align: center;
  font-weight: bolder;
  color: #f1f1f1;
}