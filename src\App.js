import React, { useState } from "react";
import PubMedComponent from "./features/PubMedComponent";
import Referances from "./Referances";
import TEReferenceDashboard from "./components/TEReferenceDashboard";
import "./App.css";

// Simple icon components
const UploadIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
  </svg>
);

const CloseIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);

function App() {
  const [terms, setTerms] = useState({isLoading:0,data:[]});
  const [isUploadDrawerOpen, setIsUploadDrawerOpen] = useState(false);

  // Close drawer when processing starts
  React.useEffect(() => {
    if (terms.isLoading > 0) {
      setIsUploadDrawerOpen(false);
    }
  }, [terms.isLoading]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Clean Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <h1 className="text-xl font-semibold text-gray-900">
              EDITINK
            </h1>
            <button
              onClick={() => setIsUploadDrawerOpen(true)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              <UploadIcon />
              <span className="ml-2">Upload</span>
            </button>
          </div>
        </div>
      </header>

      {/* Upload Drawer */}
      <div className={`upload-drawer ${isUploadDrawerOpen ? 'open' : ''}`}>
        <div className="upload-drawer-overlay" onClick={() => setIsUploadDrawerOpen(false)} />
        <div className="upload-drawer-content">
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Upload References</h2>
            <button
              onClick={() => setIsUploadDrawerOpen(false)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <CloseIcon />
            </button>
          </div>
          <div className="p-4">
            <Referances setReferences={setTerms} />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Results Section */}
        {(terms.data.length > 0 || terms.isLoading > 0) && (
          <div className="animate-fadeIn">
            <PubMedComponent terms={terms} />
          </div>
        )}

        {/* Empty State */}
        {terms.data.length === 0 && terms.isLoading === 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 mx-auto mb-4 text-gray-400">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Ready to enhance your references
            </h3>
            <p className="text-sm text-gray-600 max-w-md mx-auto">
              Click the Upload button to get started with AI-powered citation enhancement.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
