import React, { useState } from "react";
import PubMedComponent from "./features/PubMedComponent";
import Referances from "./Referances";
import TEReferenceDashboard from "./components/TEReferenceDashboard";
import "./App.css";

// Simple icon components
const SearchIcon = () => (
  <svg className="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const UploadIcon = () => (
  <svg className="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
  </svg>
);

function App() {
  const [terms, setTerms] = useState({isLoading:0,data:[]});
  const [isUploadDrawerOpen, setIsUploadDrawerOpen] = useState(true);
  const [useNewDashboard, setUseNewDashboard] = useState(false);

  // Check URL parameter for dashboard mode
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const dashboardMode = urlParams.get('dashboard');
    if (dashboardMode === 'new') {
      setUseNewDashboard(true);
    }
  }, []);

  // Auto-collapse upload drawer when processing starts
  React.useEffect(() => {
    if (terms.isLoading > 0 || terms.data.length > 0) {
      // Auto-collapse after 1 second to show the upload was successful
      const timer = setTimeout(() => {
        setIsUploadDrawerOpen(false);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [terms.isLoading, terms.data.length]);

  // Render new dashboard if requested
  if (useNewDashboard) {
    return <TEReferenceDashboard />;
  }

  return (
    <div className="dashboard-container">
      {/* Modern Header */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-flex">
            <h1 className="dashboard-title">
              TE Reference Search Dashboard
            </h1>
            <div className="header-actions">
              <button
                onClick={() => setUseNewDashboard(true)}
                className="demo-button"
                title="Try the new standalone dashboard"
              >
                🚀 Try Demo Dashboard
              </button>
              <div className="header-badge">
                <SearchIcon />
                <span className="badge-text">Powered by AI</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="main-content">
        {/* Modern Upload Section */}
        <div className={`upload-section ${isUploadDrawerOpen ? 'expanded' : 'collapsed'}`}>
          <div className="upload-header">
            <h2 className="section-title">
              <UploadIcon />
              Upload References
            </h2>
            <button
              className="section-toggle-button"
              onClick={() => setIsUploadDrawerOpen(!isUploadDrawerOpen)}
            >
              {isUploadDrawerOpen ? '▲ Collapse' : '▼ Expand'}
            </button>
          </div>

          {isUploadDrawerOpen && (
            <div className="upload-content">
              <Referances setReferences={setTerms} />
            </div>
          )}
        </div>

        {/* Results Section */}
        {(terms.data.length > 0 || terms.isLoading > 0) && (
          <div className="results-section animate-fadeInUp">
            {/* Quick Upload Toggle Button (when section is collapsed) */}
            {!isUploadDrawerOpen && (
              <div className="quick-upload-toggle">
                <button
                  className="quick-toggle-button"
                  onClick={() => setIsUploadDrawerOpen(true)}
                  title="Upload new references"
                >
                  <UploadIcon />
                  Upload New References
                </button>
              </div>
            )}

            <PubMedComponent terms={terms} />
          </div>
        )}

        {/* Empty State */}
        {terms.data.length === 0 && terms.isLoading === 0 && (
          <div className="empty-state">
            <SearchIcon />
            <h3 className="empty-state-title">
              Ready to enhance your references
            </h3>
            <p className="empty-state-subtitle">
              Upload a .docx file or paste your references above to get started with AI-powered citation enhancement.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
