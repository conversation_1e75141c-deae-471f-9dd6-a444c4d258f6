import React, { useState } from "react";
import PubMedComponent from "./features/PubMedComponent";
import Referances from "./Referances";
import TEReferenceDashboard from "./components/TEReferenceDashboard";
import "./App.css";

function App() {
  const [terms, setTerms] = useState({isLoading:0,data:[]});
  const [isUploadDrawerOpen, setIsUploadDrawerOpen] = useState(true);
  const [useNewDashboard, setUseNewDashboard] = useState(false);

  // Check URL parameter for dashboard mode
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const dashboardMode = urlParams.get('dashboard');
    if (dashboardMode === 'new') {
      setUseNewDashboard(true);
    }
  }, []);

  // Auto-collapse upload drawer when processing starts
  React.useEffect(() => {
    if (terms.isLoading > 0 || terms.data.length > 0) {
      // Auto-collapse after 1 second to show the upload was successful
      const timer = setTimeout(() => {
        setIsUploadDrawerOpen(false);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [terms.isLoading, terms.data.length]);

  // Render new dashboard if requested
  if (useNewDashboard) {
    return <TEReferenceDashboard />;
  }

  return (
    <>
      {/* Dashboard Toggle */}
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        zIndex: 1000,
        background: '#fff',
        padding: '8px 12px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        fontSize: '12px'
      }}>
        <button
          onClick={() => setUseNewDashboard(true)}
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            padding: '6px 12px',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          🚀 Try New Dashboard
        </button>
      </div>

      {/* Collapsible Upload Drawer */}
      <div className={`upload-drawer ${isUploadDrawerOpen ? 'open' : 'collapsed'}`}>
        <div className="upload-drawer-header">
          <h2>📁 Upload References</h2>
          <button
            className="drawer-toggle-button"
            onClick={() => setIsUploadDrawerOpen(!isUploadDrawerOpen)}
          >
            {isUploadDrawerOpen ? '▲ Hide Upload' : '▼ Show Upload'}
          </button>
        </div>

        {isUploadDrawerOpen && (
          <div className="upload-drawer-content">
            <Referances setReferences={setTerms} />
          </div>
        )}
      </div>

      {/* Results Section */}
      {(terms.data.length > 0 || terms.isLoading > 0) && (
        <div className="results-section">
          {/* Quick Upload Toggle Button (when drawer is collapsed) */}
          {!isUploadDrawerOpen && (
            <div className="quick-upload-toggle">
              <button
                className="quick-toggle-button"
                onClick={() => setIsUploadDrawerOpen(true)}
                title="Upload new references"
              >
                📁 Upload New References
              </button>
            </div>
          )}

          <PubMedComponent terms={terms} />
        </div>
      )}
    </>
  );
}

export default App;
