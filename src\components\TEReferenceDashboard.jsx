import React, { useState, useCallback } from 'react';

// Simple icon components to replace Lucide icons
const UploadIcon = () => (
  <svg className="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
  </svg>
);

const FileTextIcon = () => (
  <svg className="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
);

const SearchIcon = () => (
  <svg className="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const CheckCircleIcon = () => (
  <svg className="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const AlertCircleIcon = () => (
  <svg className="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const TEReferenceDashboard = () => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [textInput, setTextInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false);

  // Function to go back to original interface
  const handleGoBack = () => {
    window.location.href = window.location.pathname;
  };

  // Handle file drop
  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const validFile = files.find(file => 
      file.type === 'text/plain' || 
      file.type === 'text/csv' || 
      file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
    
    if (validFile) {
      setUploadedFile(validFile);
    }
  }, []);

  // Handle file input change
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  // Handle drag events
  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  // Simulate processing
  const handleSubmit = async () => {
    if (!uploadedFile && !textInput.trim()) return;
    
    setIsLoading(true);
    setHasSubmitted(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock results
    const mockResults = [
      {
        id: 1,
        searchTerm: "Smith, J. et al. (2023). Machine learning applications",
        enhancedCitation: "Smith, J., Johnson, A., & Williams, R. (2023). Machine learning applications in modern healthcare systems. Journal of Medical Technology, 45(3), 123-145. https://doi.org/10.1234/jmt.2023.001"
      },
      {
        id: 2,
        searchTerm: "Brown et al 2022 climate change",
        enhancedCitation: "Brown, M., Davis, K., & Thompson, L. (2022). Climate change impacts on agricultural productivity: A comprehensive review. Environmental Science & Policy, 128, 89-102. https://doi.org/10.1016/j.envsci.2022.03.015"
      },
      {
        id: 3,
        searchTerm: "AI neural networks deep learning",
        enhancedCitation: "Chen, X., Rodriguez, P., & Kim, S. (2023). Deep learning architectures for neural network optimization. Nature Machine Intelligence, 5(7), 456-471. https://doi.org/10.1038/s42256-023-00678-9"
      }
    ];
    
    setResults(mockResults);
    setIsLoading(false);
  };

  // Clear all data
  const handleClear = () => {
    setUploadedFile(null);
    setTextInput('');
    setResults([]);
    setHasSubmitted(false);
  };

  return (
    <div className="dashboard-container">
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-content">
          <div className="header-flex">
            <h1 className="dashboard-title">
              TE Reference Search Dashboard
            </h1>
            <div className="header-actions">
              <button
                onClick={handleGoBack}
                className="back-button"
                title="Return to original interface"
              >
                ← Back to Original
              </button>
              <div className="header-badge">
                <SearchIcon />
                <span className="badge-text">Powered by AI</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="main-content">
        {/* Upload Section */}
        <div className="upload-section">
          <h2 className="section-title">
            <UploadIcon />
            Upload References
          </h2>

          <div className="upload-grid">
            {/* File Upload */}
            <div className="upload-column">
              <label className="upload-label">
                Upload File (.txt, .csv, .docx)
              </label>
              <div
                className={`upload-dropzone ${
                  isDragOver
                    ? 'dropzone-active'
                    : uploadedFile
                      ? 'dropzone-success'
                      : 'dropzone-default'
                }`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
              >
                <input
                  type="file"
                  accept=".txt,.csv,.docx"
                  onChange={handleFileChange}
                  className="file-input"
                />

                {uploadedFile ? (
                  <div className="upload-success">
                    <CheckCircleIcon />
                    <p className="success-filename">{uploadedFile.name}</p>
                    <p className="success-message">File ready for processing</p>
                  </div>
                ) : (
                  <div className="upload-placeholder">
                    <FileTextIcon />
                    <p className="placeholder-title">
                      Drop your file here or click to browse
                    </p>
                    <p className="placeholder-subtitle">
                      Supports .txt, .csv, and .docx files
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Text Input */}
            <div className="upload-column">
              <label className="upload-label">
                Paste your references here
              </label>
              <textarea
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                placeholder="Paste your references here, one per line..."
                className="text-input"
                rows={8}
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="submit-section">
            <button
              onClick={handleSubmit}
              disabled={(!uploadedFile && !textInput.trim()) || isLoading}
              className="submit-button"
            >
              {isLoading ? (
                <>
                  <div className="loading-spinner"></div>
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <SearchIcon />
                  <span>Search & Enhance</span>
                </>
              )}
            </button>

            {hasSubmitted && (
              <button
                onClick={handleClear}
                className="clear-button"
              >
                Clear All
              </button>
            )}
          </div>
        </div>

        {/* Results Section */}
        {hasSubmitted && (
          <div className="results-section animate-fadeInUp">
            <h2 className="results-title">
              <CheckCircleIcon />
              Enhanced References
            </h2>

            {isLoading ? (
              <div className="loading-state">
                <div className="large-spinner"></div>
                <p className="loading-text">Processing your references...</p>
              </div>
            ) : results.length > 0 ? (
              <div className="table-container">
                <table className="results-table">
                  <thead>
                    <tr className="table-header">
                      <th className="table-th index-column">Index</th>
                      <th className="table-th">Search Term</th>
                      <th className="table-th">Enhanced Citation</th>
                    </tr>
                  </thead>
                  <tbody>
                    {results.map((result, index) => (
                      <tr
                        key={result.id}
                        className="table-row"
                        style={{ animationDelay: `${index * 100}ms` }}
                      >
                        <td className="table-td index-cell">
                          {result.id}
                        </td>
                        <td className="table-td search-term-cell">
                          <div className="truncate-text" title={result.searchTerm}>
                            {result.searchTerm}
                          </div>
                        </td>
                        <td className="table-td citation-cell">
                          <div className="line-clamp-3">
                            {result.enhancedCitation}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="empty-results">
                <AlertCircleIcon />
                <h3 className="empty-title">No references found</h3>
                <p className="empty-subtitle">
                  Please check your input and try again.
                </p>
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {!hasSubmitted && (
          <div className="empty-state">
            <SearchIcon />
            <h3 className="empty-state-title">
              Ready to enhance your references
            </h3>
            <p className="empty-state-subtitle">
              Upload a file or paste your references above to get started with AI-powered citation enhancement.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TEReferenceDashboard;
