import React, { useState } from "react";
import axios from "axios";
import { upload } from "../src/constants/urls";
import { ErrorNotification } from "./components/common";
const Referances = ({ setReferences }) => {
  const [file, setFile] = useState(null);
  const [isError, setIsError] = useState("");
  const [localReferences, setLocalReferences] = useState([]);
  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
  };

  const handleUpload = async () => {
    if (!file) {
      setIsError("Please select a file first!");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    setReferences({ isLoading: 1, data: [] });
    setIsError("");
    try {
      const response = await axios.post(upload, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
          "Access-Control-Allow-Origin": "*",
        },
      });
      setReferences({ isLoading: 2, data: response.data.references });
    } catch (error) {
      console.error("Error uploading the file:", error);
    }
  };

  const handleAreaUpload = async () => {
    if(localReferences.length === 0){
      setIsError("Please enter references or seclect file!");
      return;
    }
    setReferences({ isLoading: 2, data: localReferences });
  };

  function splitReferences(text) {
    return text.split(/\n+/).filter(Boolean).map((ref, index) => `${index + 1}. ${ref.trim()}`);
}


  const handleAreaChange = (e) => {
    setLocalReferences(splitReferences(e.target.value.trim()));
  };
  return (
    <>
      <div className="upload-grid">
        {/* File Upload Section */}
        <div className="upload-column">
          <label className="upload-label">
            Upload .docx File
          </label>
          <div className="file-upload-area">
            <input
              className="file-input"
              type="file"
              onChange={handleFileChange}
              accept=".docx"
            />
            <div className="upload-placeholder">
              <svg className="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="placeholder-title">
                {file ? file.name : "Click to select .docx file"}
              </p>
              <p className="placeholder-subtitle">
                Supports Microsoft Word documents
              </p>
            </div>
          </div>
          <button className="submit-button" onClick={handleUpload}>
            <svg className="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            Upload File
          </button>
        </div>

        {/* Text Input Section */}
        <div className="upload-column">
          <label className="upload-label">
            Paste References
          </label>
          <textarea
            onChange={handleAreaChange}
            className="text-input"
            placeholder="Paste your references here, one per line..."
            rows={8}
          />
          <button className="submit-button" onClick={handleAreaUpload}>
            <svg className="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            Process References
          </button>
        </div>
      </div>

      {isError && <ErrorNotification message={isError} />}
    </>
  );
};

export default Referances;
