import React, { useState } from "react";
import "../App.css";
import useFetchPubMedData from "../hooks/useFetchPubMedData";
import { sortTwoArray } from "../services/helpers/arrayHelpers";
import { searchInScholar } from "./services/searchInScholar";
import { Di<PERSON><PERSON><PERSON><PERSON>er, NotFoundReferences, ErrorNotification, LoaderSpinner } from "../components/common";

const PubMed = ({ searchState }) => {
  const [isDiffViewerOpen, setIsDiffViewerOpen] = useState(false);
  const { data, loading, error, setData, setLoading } = useFetchPubMedData(searchState.data);

  const handleRetryWithScholar = async () => {
    setLoading(true);
    try {
      const { scholarArray, notFoundSCArray } = await searchInScholar(data.notFound);
      const foundSortedArray = sortTwoArray(data.found, scholarArray);
      const notFoundSortedArray = sortTwoArray(data.notFound, notFoundSCArray);

      setData(prev => ({
        ...prev,
        found: foundSortedArray,
        notFound: notFoundSortedArray,
      }));
    } catch (error) {
      console.error("Scholar search error:", error);
    } finally {
      setLoading(false);
    }
  };

  if (searchState.isLoading === 2 && searchState.data.length === 0) {
    return <NotFoundReferences />;
  }

  if (error || typeof data === "string") {
    return <ErrorNotification message={error || data} />;
  }

  return (
    <div className="pubmed-container">
      {loading && (
        <div className="loading-container">
          <LoaderSpinner />
          <p>Processing citations...</p>
        </div>
      )}

      {!loading && (
        <>
          <div className="found-citation-wrapper">
            <div className="download-wrapper">
              <button
                onClick={() => setIsDiffViewerOpen(prev => !prev)}
                className="toggle-button"
              >
                {isDiffViewerOpen ? "Hide Diff" : "Show Diff"}
              </button>
            </div>
          </div>

          <CitationTable
            citations={data?.found}
            isDiffViewerOpen={isDiffViewerOpen}
          />

          {data?.notFoundArray?.length > 0 && (
            <NotFoundBox
              title="Not Found Citations"
              notFound={data.notFoundArray}
              onRetry={handleRetryWithScholar}
            />
          )}

          {data?.urlFound?.length > 0 && (
            <NotFoundBox
              title="URL Citations"
              notFound={data.urlFound}
            />
          )}
        </>
      )}
    </div>
  );
};

const CitationTable = ({ citations, isDiffViewerOpen }) => {
  if (!citations?.length) return null;

  return (
    <table className="table">
      <thead>
        <tr>
          <th>Reference</th>
          {isDiffViewerOpen ? (
            <th>Comparison</th>
          ) : (
            <>
              <th>Original Citation</th>
              <th>Enhanced Citation</th>
            </>
          )}
        </tr>
      </thead>
      <tbody>
        {citations.map((elem, ind) => (
          <DetailedBox
            elem={elem}
            key={`found-${ind}`}
            isDiffViewerOpen={isDiffViewerOpen}
          />
        ))}
      </tbody>
    </table>
  );
};

const DetailedBox = ({ elem, isDiffViewerOpen }) => {
  if (typeof elem === "string") {
    return;
  }
  return (
    <tr>
      <td>{elem.ind + 1}</td>
      {isDiffViewerOpen ? (
        <td>
          <DiffHighlighter
            text1={elem?.term}
            text2={elem?.finalStr}
          />{" "}
        </td>
      ) : (
        <>
          <td>{elem.term}</td>
          <td> {elem.finalStr} </td>
        </>
      )}
    </tr>
  );
};

const NotFoundBox = ({ title, notFound, retryWithScholerFunction }) => {
  return (
    <>
      <div>
        <h1>{title} </h1>
      </div>
      <table className="table">
        <tr>
          <th>Index </th>
          <th>
            <strong>Title</strong>
          </th>
        </tr>
        {notFound.map((elem, i) => (
          <Element elem={elem} key={"abs" + i} />
        ))}
      </table>
    </>
  );
};

const Element = ({ elem }) => {
  return (
    <tr>
      <td>{elem.ind + 1}</td>
      <td>{elem.term}</td>
    </tr>
  );
};

const LoaderSpinner = () => {
  return (
    <div className="fixed-overlay">
      <div className="loader-container">
        <div className="loader"></div>
      </div>
    </div>
  );
};
export default PubMed;
